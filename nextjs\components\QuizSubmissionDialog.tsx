'use client';

import { DownloadIcon, LoaderIcon } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import type { QuizData, QuizResponse } from '@/types/quizType';

interface QuizSubmissionDialogProps {
  isOpen: boolean;
  isLoading: boolean;
  pdfResult: QuizResponse | null;
  quizData: QuizData | null;
  onDownloadAction: (name: string) => void;
  onCloseAction: () => void;
}

export function QuizSubmissionDialog({
  isOpen,
  isLoading,
  pdfResult,
  quizData,
  onDownloadAction,
  onCloseAction: onClose,
}: QuizSubmissionDialogProps) {
  const isSuccess = pdfResult?.success === true;
  const hasDownloadableContent = isSuccess && pdfResult?.pdfData;

  const handleDownload = () => {
    if (quizData?.personalInfo?.name) {
      onDownloadAction(quizData.personalInfo.name);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md" showCloseButton={!isLoading}>
        <DialogHeader>
          <DialogTitle>
            {isLoading
              ? '正在生成报告...'
              : isSuccess
                ? '报告生成成功！'
                : '生成失败'}
          </DialogTitle>
          <DialogDescription>
            {isLoading &&
              '请稍候，我们正在根据您的问卷生成个性化的职业规划报告。'}
            {isSuccess &&
              hasDownloadableContent &&
              '您的职业规划报告已生成完成，点击下方按钮下载。'}
            {isSuccess &&
              !hasDownloadableContent &&
              '报告生成成功，但下载链接暂时不可用。'}
            {!isLoading &&
              !isSuccess &&
              (pdfResult?.message || '报告生成失败，请稍后重试。')}
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center gap-4 py-4">
          {isLoading && (
            <>
              <LoaderIcon className="text-primary h-8 w-8 animate-spin" />
              <Progress value={undefined} className="w-full" />
            </>
          )}

          {isSuccess && hasDownloadableContent && (
            <div className="flex flex-col items-center gap-2">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100 dark:bg-green-900">
                <DownloadIcon className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
            </div>
          )}

          {!isLoading && !isSuccess && (
            <div className="flex flex-col items-center gap-2">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-red-100 dark:bg-red-900">
                <span className="text-2xl">❌</span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          {isLoading && (
            <Button variant="outline" disabled>
              请等待...
            </Button>
          )}

          {isSuccess && hasDownloadableContent && (
            <>
              <Button onClick={handleDownload} className="gap-2">
                <DownloadIcon className="h-4 w-4" />
                下载报告
              </Button>
            </>
          )}

          {!isLoading && (!isSuccess || !hasDownloadableContent) && (
            <Button onClick={onClose}>关闭</Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
