{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^react$", "^next", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "tailwindFunctions": ["clsx", "cn", "cva"]}