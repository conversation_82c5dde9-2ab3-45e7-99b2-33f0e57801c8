import type { Metada<PERSON> } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';

import { Toaster } from '@/components/ui/sonner';

import './globals.css';
import Providers from './providers';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'CareerGuide',
  description: '生涯通',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Toaster />
        <Providers>
          <div className="flex min-h-screen items-center justify-center p-4">
            <div className="w-auto">{children}</div>
          </div>
        </Providers>
      </body>
    </html>
  );
}
