import type { QuizData, QuizResponse } from '@/types/quizType';

import customFetch, { ApiError } from './customFetch';

export class QuizApi {
  private baseUrl = 'http://localhost:8000';

  /**
   * 提交问卷并获取PDF结果
   */
  async submitQuiz(data: QuizData): Promise<QuizResponse> {
    try {
      // 使用 customFetch 的 requestBlob 方法处理 PDF 响应
      const response = await customFetch.requestBlob(
        `${this.baseUrl}/generate-report`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }
      );

      // 检查响应类型
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/pdf')) {
        // 如果返回的是PDF文件
        return {
          success: true,
          pdfData: response.blob,
        };
      } else {
        throw new Error('Unexpected response type');
      }
    } catch (error) {
      console.error('提交问卷失败:', error);

      // 处理 ApiError
      if (error instanceof ApiError) {
        return {
          success: false,
          message: error.message,
        };
      }

      return {
        success: false,
        message: error instanceof Error ? error.message : '提交失败',
      };
    }
  }
}

// 创建API实例
const quizApi = new QuizApi();

export default quizApi;
