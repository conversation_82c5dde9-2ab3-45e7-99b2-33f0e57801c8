'use client';

import { useState } from 'react';

import Image from 'next/image';

import {
  BookOpen,
  ChevronLeft,
  ChevronRight,
  LibraryBig,
  Trophy,
  User,
  Users,
} from 'lucide-react';

import { QuizSubmissionDialog } from '@/components/QuizSubmissionDialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useSubmitQuiz } from '@/hooks/useQuiz';
import { QuizData } from '@/types/quizType';

const sections = [
  {
    id: 'personal',
    title: '个人信息',
    icon: User,
    color: 'bg-blue-500',
  },
  {
    id: 'organization',
    title: '组织生活规划',
    icon: Users,
    color: 'bg-green-500',
  },
  {
    id: 'academic',
    title: '学业规划',
    icon: BookOpen,
    color: 'bg-purple-500',
  },
  {
    id: 'further-study',
    title: '升学规划',
    icon: Trophy,
    color: 'bg-orange-500',
  },
  {
    id: 'career',
    title: '就业规划',
    icon: LibraryBig,
    color: 'bg-red-500',
  },
];

export default function QuestionnairePage() {
  const [currentSection, setCurrentSection] = useState(0);
  const [formData, setFormData] = useState<QuizData>({
    personalInfo: {
      name: '',
      major: '',
      currentYear: '',
      schoolName: '',
    },
    organizationInterests: [],
    academicInterests: [],
    furtherStudyInterests: [],
    careerInterests: [],
  });

  // 使用提交问卷的hook
  const {
    mutate: submitQuiz, // 将 useMutation 返回的 mutate 函数重命名为 submitQuiz
    isPending,
    isDialogOpen,
    pdfResult,
    quizData,
    downloadPdf,
    closeDialog,
  } = useSubmitQuiz();

  const progress = ((currentSection + 1) / sections.length) * 100;

  const handleNext = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const handleSubmit = () => {
    console.log('提交问卷数据:', formData);
    submitQuiz(formData);
  };

  const updateFormData = (field: keyof QuizData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCheckboxChange = (
    field: keyof QuizData,
    value: string,
    checked: boolean
  ) => {
    const currentArray = formData[field] as string[];
    if (checked) {
      updateFormData(field, [...currentArray, value]);
    } else {
      updateFormData(
        field,
        currentArray.filter(item => item !== value)
      );
    }
  };

  // 全选/取消全选功能
  const handleSelectAll = (field: keyof QuizData, options: string[]) => {
    const currentArray = formData[field] as string[];
    const isAllSelected = options.every(option =>
      currentArray.includes(option)
    );

    if (isAllSelected) {
      // 如果全部选中，则取消全选
      updateFormData(
        field,
        currentArray.filter(item => !options.includes(item))
      );
    } else {
      // 如果不是全部选中，则全选
      const newArray = [...new Set([...currentArray, ...options])];
      updateFormData(field, newArray);
    }
  };

  return (
    <div className="bg-gradient-to-br0 h-auto w-200">
      <div className="container mx-auto px-4">
        {/* LOGO */}
        <div className="flex justify-center">
          <Image
            src="/logo.jpg"
            alt="智绘大学生涯 Logo"
            width={400}
            height={200}
            className="object-contain"
          />
        </div>
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="mb-4 text-3xl font-bold">
            智绘大学生涯•AI入学发展报告
          </h1>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <Progress value={progress} className="h-2" />
        </div>

        {/* Form Content */}
        <Card className="mx-auto max-w-4xl">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div
                className={`rounded-lg p-2 ${sections[currentSection].color}`}
              >
                {(() => {
                  const Icon = sections[currentSection].icon;
                  return <Icon className="h-6 w-6 text-white" />;
                })()}
              </div>
              <div>
                <CardTitle className="text-2xl">
                  {sections[currentSection].title}
                </CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 第一部分：个人信息 */}
            {currentSection === 0 && (
              <div className="space-y-6">
                <div>
                  <Label htmlFor="name" className="text-base font-semibold">
                    姓名
                  </Label>
                  <Input
                    id="name"
                    placeholder="请输入您的姓名"
                    value={formData.personalInfo.name}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        personalInfo: {
                          ...prev.personalInfo,
                          name: e.target.value,
                        },
                      }))
                    }
                    className="mt-2"
                  />
                </div>

                <Separator />

                <div>
                  <Label htmlFor="major" className="text-base font-semibold">
                    专业
                  </Label>
                  <Input
                    id="major"
                    placeholder="请输入您的专业"
                    value={formData.personalInfo.major}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        personalInfo: {
                          ...prev.personalInfo,
                          major: e.target.value,
                        },
                      }))
                    }
                    className="mt-2"
                  />
                </div>

                <Separator />

                <div>
                  <Label
                    htmlFor="currentYear"
                    className="text-base font-semibold"
                  >
                    年级
                  </Label>
                  <Select
                    value={formData.personalInfo.currentYear}
                    onValueChange={value =>
                      setFormData(prev => ({
                        ...prev,
                        personalInfo: {
                          ...prev.personalInfo,
                          currentYear: value,
                        },
                      }))
                    }
                  >
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="请选择您的年级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="大一">大一</SelectItem>
                      <SelectItem value="大二">大二</SelectItem>
                      <SelectItem value="大三">大三</SelectItem>
                      <SelectItem value="大四">大四</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div>
                  <Label
                    htmlFor="schoolName"
                    className="text-base font-semibold"
                  >
                    院校
                  </Label>
                  <Input
                    id="schoolName"
                    placeholder="请输入您的学校名称"
                    value={formData.personalInfo.schoolName}
                    onChange={e =>
                      setFormData(prev => ({
                        ...prev,
                        personalInfo: {
                          ...prev.personalInfo,
                          schoolName: e.target.value,
                        },
                      }))
                    }
                    className="mt-2"
                  />
                </div>
              </div>
            )}

            {/* 第二部分：组织生活规划 */}
            {currentSection === 1 && (
              <div className="space-y-6">
                <div>
                  {(() => {
                    const organizationOptions = [
                      '入团意义与流程',
                      '入党申请与党员发展',
                      '学生组织与社团参与',
                      '入伍政策解读',
                      '学工助理的角色与职责',
                      '勤工俭学的机会与挑战',
                      '防诈骗攻略与安全意识',
                    ];
                    const isAllSelected = organizationOptions.every(option =>
                      formData.organizationInterests.includes(option)
                    );

                    return (
                      <>
                        <div className="mb-3 flex space-x-2">
                          <Label className="text-base font-semibold">
                            您对以下组织生活规划内容的兴趣程度
                          </Label>
                          {/* 全选选项 */}
                          <div className="borderp-3 my-3 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="select-all-organization"
                                checked={isAllSelected}
                                onCheckedChange={() =>
                                  handleSelectAll(
                                    'organizationInterests',
                                    organizationOptions
                                  )
                                }
                              />
                              <Label
                                htmlFor="select-all-organization"
                                className="text-sm font-medium text-blue-600"
                              >
                                全选
                              </Label>
                            </div>
                          </div>
                        </div>
                        {/* 选项列表 */}
                        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                          {organizationOptions.map(interest => (
                            <div
                              key={interest}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={interest}
                                checked={formData.organizationInterests.includes(
                                  interest
                                )}
                                onCheckedChange={checked =>
                                  handleCheckboxChange(
                                    'organizationInterests',
                                    interest,
                                    checked as boolean
                                  )
                                }
                              />
                              <Label htmlFor={interest} className="text-sm">
                                {interest}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}

            {/* 第三部分：学业规划 */}
            {currentSection === 2 && (
              <div className="space-y-6">
                <div>
                  {(() => {
                    const academicOptions = [
                      '绩点的重要性与计算方法',
                      '提升绩点的策略与技巧',
                      '专业分流的决策因素',
                      '转专业的流程与准备',
                      '辅修的意义与选择',
                      '第二学位的规划与实施',
                      '专业证书的重要性',
                      '考证规划的时间表与准备',
                      '奖学金的种类与申请技巧',
                      '竞赛的分类与选择',
                      '竞赛准备与经验分享',
                      '实习的重要性与选择',
                      '实习申请流程与经验积累',
                    ];
                    const isAllSelected = academicOptions.every(option =>
                      formData.academicInterests.includes(option)
                    );

                    return (
                      <>
                        <div className="mb-3 flex space-x-2">
                          <Label className="text-base font-semibold">
                            您对以下学业规划内容的兴趣程度
                          </Label>
                          {/* 全选选项 */}
                          <div className="borderp-3 my-3 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="select-all-academic"
                                checked={isAllSelected}
                                onCheckedChange={() =>
                                  handleSelectAll(
                                    'academicInterests',
                                    academicOptions
                                  )
                                }
                              />
                              <Label
                                htmlFor="select-all-academic"
                                className="text-sm font-medium text-blue-600"
                              >
                                全选
                              </Label>
                            </div>
                          </div>
                        </div>

                        {/* 选项列表 */}
                        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                          {academicOptions.map(interest => (
                            <div
                              key={interest}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={interest}
                                checked={formData.academicInterests.includes(
                                  interest
                                )}
                                onCheckedChange={checked =>
                                  handleCheckboxChange(
                                    'academicInterests',
                                    interest,
                                    checked as boolean
                                  )
                                }
                              />
                              <Label htmlFor={interest} className="text-sm">
                                {interest}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}

            {/* 第四部分：升学规划 */}
            {currentSection === 3 && (
              <div className="space-y-6">
                <div>
                  {(() => {
                    const furtherStudyOptions = [
                      '留学国家与院校选择',
                      '留学申请流程与材料准备',
                      '考研专业与院校选择',
                      '考研复习计划与时间管理',
                      '保研条件与申请流程',
                      '保研材料准备与面试技巧',
                    ];
                    const isAllSelected = furtherStudyOptions.every(option =>
                      formData.furtherStudyInterests.includes(option)
                    );

                    return (
                      <>
                        <div className="mb-3 flex space-x-2">
                          <Label className="text-base font-semibold">
                            您对以下升学规划内容的兴趣程度
                          </Label>
                          {/* 全选选项 */}
                          <div className="borderp-3 my-3 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="select-all-further-study"
                                checked={isAllSelected}
                                onCheckedChange={() =>
                                  handleSelectAll(
                                    'furtherStudyInterests',
                                    furtherStudyOptions
                                  )
                                }
                              />
                              <Label
                                htmlFor="select-all-further-study"
                                className="text-sm font-medium text-blue-600"
                              >
                                全选
                              </Label>
                            </div>
                          </div>
                        </div>

                        {/* 选项列表 */}
                        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                          {furtherStudyOptions.map(interest => (
                            <div
                              key={interest}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={interest}
                                checked={formData.furtherStudyInterests.includes(
                                  interest
                                )}
                                onCheckedChange={checked =>
                                  handleCheckboxChange(
                                    'furtherStudyInterests',
                                    interest,
                                    checked as boolean
                                  )
                                }
                              />
                              <Label htmlFor={interest} className="text-sm">
                                {interest}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}

            {/* 第五部分：就业规划 */}
            {currentSection === 4 && (
              <div className="space-y-6">
                <div>
                  {(() => {
                    const careerOptions = [
                      '公务员考试与事业单位考试概览',
                      '考公考编的备考策略与时间规划',
                      '职业定位与市场分析',
                      '求职材料准备与面试技巧',
                      '简历撰写的基本原则与技巧',
                      '个人品牌建设与网络形象管理',
                    ];
                    const isAllSelected = careerOptions.every(option =>
                      formData.careerInterests.includes(option)
                    );

                    return (
                      <>
                        <div className="mb-3 flex space-x-2">
                          <Label className="text-base font-semibold">
                            您对以下就业规划内容的兴趣程度
                          </Label>
                          {/* 全选选项 */}
                          <div className="borderp-3 my-3 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="select-all-career"
                                checked={isAllSelected}
                                onCheckedChange={() =>
                                  handleSelectAll(
                                    'careerInterests',
                                    careerOptions
                                  )
                                }
                              />
                              <Label
                                htmlFor="select-all-career"
                                className="text-sm font-medium text-blue-600"
                              >
                                全选
                              </Label>
                            </div>
                          </div>
                        </div>

                        {/* 选项列表 */}
                        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                          {careerOptions.map(interest => (
                            <div
                              key={interest}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={interest}
                                checked={formData.careerInterests.includes(
                                  interest
                                )}
                                onCheckedChange={checked =>
                                  handleCheckboxChange(
                                    'careerInterests',
                                    interest,
                                    checked as boolean
                                  )
                                }
                              />
                              <Label htmlFor={interest} className="text-sm">
                                {interest}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}
          </CardContent>

          {/* Navigation Buttons */}
          <div className="flex justify-between p-6 pt-0">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentSection === 0}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              上一步
            </Button>

            {currentSection === sections.length - 1 ? (
              <Button
                onClick={handleSubmit}
                className="flex items-center gap-2"
              >
                提交问卷
              </Button>
            ) : (
              <Button onClick={handleNext} className="flex items-center gap-2">
                下一步
                <ChevronRight className="h-4 w-4" />
              </Button>
            )}
          </div>
        </Card>
      </div>

      {/* 问卷提交对话框 */}
      <QuizSubmissionDialog
        isOpen={isDialogOpen}
        isLoading={isPending}
        pdfResult={pdfResult}
        quizData={quizData}
        onDownloadAction={downloadPdf}
        onCloseAction={closeDialog}
      />
    </div>
  );
}
